#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel数据填充到Prompt模板脚本 (版本2)
绕过Excel格式问题，支持多种数据源：
1. CSV文件（如果已转换）
2. 手动创建的示例数据
3. 从现有Excel文件提取的数据结构
"""

import pandas as pd
import os
import json
import csv
from typing import Dict, List, Optional

class RobustPromptGenerator:
    """稳定的Prompt生成器"""
    
    def __init__(self, output_dir: str = "generated_prompts"):
        self.output_dir = output_dir
        self.data = None
        self.column_mapping = {
            'session_id': 'sessionId',
            'case': 'case', 
            'signal': '信号',
            'outbound_record': '外呼记录',
            'dialog_history': '对话历史',
            'knowledge_list': '业务知识列表'
        }
        
        # 创建输出目录
        os.makedirs(self.output_dir, exist_ok=True)
    
    def load_data_from_csv(self, csv_path: str) -> bool:
        """
        从CSV文件加载数据
        
        Args:
            csv_path: CSV文件路径
            
        Returns:
            是否成功加载
        """
        if not os.path.exists(csv_path):
            print(f"❌ CSV文件不存在: {csv_path}")
            return False
        
        try:
            print(f"从CSV文件加载数据: {csv_path}")
            
            # 尝试多种编码
            encodings = ['utf-8-sig', 'utf-8', 'gbk', 'cp1252']
            
            for encoding in encodings:
                try:
                    self.data = pd.read_csv(
                        csv_path,
                        encoding=encoding,
                        dtype={'case': str, 'sessionId': str},  # 确保关键列为字符串
                        na_filter=False
                    )
                    print(f"✅ 成功加载数据，形状: {self.data.shape}")
                    return True
                except:
                    continue
            
            print("❌ 所有编码方式都失败")
            return False
            
        except Exception as e:
            print(f"❌ 加载CSV数据失败: {str(e)}")
            return False
    
    def create_sample_data(self) -> bool:
        """
        创建示例数据用于测试
        
        Returns:
            是否成功创建
        """
        print("创建示例数据进行测试...")
        
        # 基于已知的Excel结构创建示例数据
        sample_data = []
        
        for i in range(5):  # 创建5行示例数据
            row = {
                'sessionId': f'{i+1}',
                'case': f'case_{i+1:013d}',
                '信号': f'{{现在时间:20250807 10:0{i}\n{{用户是否已下单:是 下单时间为20250807 09:{i:02d}}}',
                '外呼记录': '空' if i % 2 == 0 else f'外呼记录_{i+1}',
                '对话历史': f'用户:我的订单有问题\n用户:这是订单{i+1}\n客服:好的，我来帮您处理...',
                '业务知识列表': f'{20+i}.当用户反馈"我的订单超时了"或"我的餐品怎么还没送到"时，客服应该...'
            }
            sample_data.append(row)
        
        self.data = pd.DataFrame(sample_data)
        print(f"✅ 创建了 {len(sample_data)} 行示例数据")
        return True
    
    def load_prompt_template(self, template_path: str) -> Optional[str]:
        """
        加载Prompt模板
        
        Args:
            template_path: 模板文件路径
            
        Returns:
            模板内容或None
        """
        if not os.path.exists(template_path):
            print(f"❌ 模板文件不存在: {template_path}")
            return None
        
        try:
            with open(template_path, 'r', encoding='utf-8') as f:
                template = f.read()
            print(f"✅ 成功加载模板文件: {template_path}")
            return template
        except Exception as e:
            print(f"❌ 加载模板文件失败: {str(e)}")
            return None
    
    def create_default_template(self) -> str:
        """
        创建默认的Prompt模板
        
        Returns:
            默认模板内容
        """
        default_template = """# 智能客服对话分析

## 业务知识
{{knowledge}}

## 系统信号
{{order_signal}}

## 外呼记录
{{waihu_signal}}

## 对话历史
{{context}}

---
请基于以上信息进行客服对话分析和建议。
"""
        
        # 保存默认模板
        default_template_path = os.path.join(os.path.dirname(self.output_dir), "Default-Prompt-Template")
        with open(default_template_path, 'w', encoding='utf-8') as f:
            f.write(default_template)
        
        print(f"✅ 创建默认模板: {default_template_path}")
        return default_template
    
    def fill_prompt_template(self, template: str, row_data: Dict[str, str]) -> str:
        """
        填充Prompt模板
        
        Args:
            template: 模板内容
            row_data: 行数据字典
            
        Returns:
            填充后的Prompt
        """
        # 获取各字段的值
        knowledge = row_data.get(self.column_mapping['knowledge_list'], '')
        order_signal = row_data.get(self.column_mapping['signal'], '')
        waihu_signal = row_data.get(self.column_mapping['outbound_record'], '')
        context = row_data.get(self.column_mapping['dialog_history'], '')
        
        # 处理空值
        knowledge = knowledge if pd.notna(knowledge) and knowledge.strip() else "暂无业务知识"
        order_signal = order_signal if pd.notna(order_signal) and order_signal.strip() else "暂无系统信号"
        waihu_signal = waihu_signal if pd.notna(waihu_signal) and waihu_signal.strip() else "暂无外呼记录"
        context = context if pd.notna(context) and context.strip() else "暂无对话历史"
        
        # 替换模板变量
        filled_template = template.replace('{{knowledge}}', str(knowledge))
        filled_template = filled_template.replace('{{order_signal}}', str(order_signal))
        filled_template = filled_template.replace('{{waihu_signal}}', str(waihu_signal))
        filled_template = filled_template.replace('{{context}}', str(context))
        
        return filled_template
    
    def generate_prompts(self, template_path: Optional[str] = None) -> int:
        """
        生成Prompt文件
        
        Args:
            template_path: 模板文件路径，None则使用默认模板
            
        Returns:
            生成的Prompt数量
        """
        if self.data is None or self.data.empty:
            print("❌ 没有数据可处理")
            return 0
        
        # 加载模板
        if template_path:
            template = self.load_prompt_template(template_path)
            if template is None:
                template = self.create_default_template()
        else:
            template = self.create_default_template()
        
        print(f"开始生成Prompt文件...")
        generated_count = 0
        
        for index, row in self.data.iterrows():
            try:
                # 获取会话ID
                session_id = row.get(self.column_mapping['session_id'], f'session_{index+1}')
                
                # 创建行数据字典
                row_data = row.to_dict()
                
                # 填充模板
                filled_prompt = self.fill_prompt_template(template, row_data)
                
                # 保存到文件
                output_filename = f"prompt_{session_id}.txt"
                output_path = os.path.join(self.output_dir, output_filename)
                
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(filled_prompt)
                
                generated_count += 1
                
                if generated_count % 10 == 0:
                    print(f"已生成 {generated_count} 个Prompt文件...")
                    
            except Exception as e:
                print(f"处理第{index+1}行时出错: {str(e)}")
                continue
        
        print(f"✅ 成功生成 {generated_count} 个Prompt文件")
        
        # 生成统计信息
        self._save_generation_stats(generated_count, template_path)
        
        return generated_count
    
    def _save_generation_stats(self, count: int, template_path: Optional[str]):
        """保存生成统计信息"""
        stats = {
            'total_prompts': count,
            'template_file': template_path or '默认模板',
            'output_directory': self.output_dir,
            'columns_mapping': self.column_mapping,
            'data_source': '示例数据' if template_path is None else 'CSV文件',
            'generation_timestamp': pd.Timestamp.now().isoformat()
        }
        
        stats_path = os.path.join(self.output_dir, 'generation_stats.json')
        with open(stats_path, 'w', encoding='utf-8') as f:
            json.dump(stats, f, ensure_ascii=False, indent=2)
        
        print(f"📊 统计信息已保存: {stats_path}")
    
    def display_data_summary(self):
        """显示数据摘要"""
        if self.data is None:
            print("❌ 没有数据")
            return
        
        print("\n" + "="*60)
        print("📊 数据摘要")
        print("="*60)
        print(f"总行数: {len(self.data)}")
        print(f"总列数: {len(self.data.columns)}")
        print()
        
        print("📋 列映射:")
        for key, col_name in self.column_mapping.items():
            if col_name in self.data.columns:
                non_empty = (self.data[col_name].str.strip() != '').sum()
                fill_rate = non_empty / len(self.data) * 100
                print(f"  {key}: {col_name} ({non_empty}/{len(self.data)}, {fill_rate:.1f}%)")
            else:
                print(f"  {key}: {col_name} (列不存在)")
        print()
        
        # 数据预览
        print("📋 数据预览 (前2行):")
        for key, col_name in list(self.column_mapping.items())[:4]:  # 只显示前4个关键列
            if col_name in self.data.columns:
                print(f"\n{key} ({col_name}):")
                for i in range(min(2, len(self.data))):
                    value = str(self.data.iloc[i][col_name])[:100]
                    if len(str(self.data.iloc[i][col_name])) > 100:
                        value += "..."
                    print(f"  第{i+1}行: {value}")

def main():
    """主函数"""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 文件路径配置
    csv_files = [
        os.path.join(current_dir, "待解析1.csv"),  # 新上传的CSV文件
        os.path.join(current_dir, "待解析.csv"),  # 之前的CSV文件
        os.path.join(current_dir, "manual_data.csv"),  # 手动转换的CSV
        os.path.join(current_dir, "pandas_converted_data.csv"),  # pandas转换的CSV
        os.path.join(current_dir, "data_direct_conversion.csv")  # 直接转换的CSV
    ]
    
    template_path = os.path.join(current_dir, "Prompt-template")
    output_dir = os.path.join(current_dir, "generated_prompts")
    
    print("=== 稳定Prompt生成器 (版本2) ===")
    print(f"输出目录: {output_dir}")
    print(f"模板文件: {template_path}")
    print("-" * 60)
    
    # 创建生成器
    generator = RobustPromptGenerator(output_dir)
    
    # 尝试加载数据
    data_loaded = False
    
    # 首先尝试从CSV文件加载
    for csv_path in csv_files:
        if os.path.exists(csv_path):
            print(f"\n尝试加载CSV文件: {csv_path}")
            if generator.load_data_from_csv(csv_path):
                data_loaded = True
                break
    
    # 如果CSV加载失败，创建示例数据
    if not data_loaded:
        print("\n没有找到可用的CSV文件，使用示例数据...")
        if not generator.create_sample_data():
            print("❌ 示例数据创建失败")
            return
    
    # 显示数据摘要
    generator.display_data_summary()
    
    # 生成Prompt文件
    try:
        count = generator.generate_prompts(template_path if os.path.exists(template_path) else None)
        print(f"\n🎉 任务完成！共生成 {count} 个Prompt文件")
        
        if count > 0:
            print(f"📁 输出目录: {output_dir}")
            
            # 显示生成的文件列表（前几个）
            files = [f for f in os.listdir(output_dir) if f.startswith('prompt_')]
            if files:
                print(f"📄 生成的文件示例:")
                for f in files[:3]:
                    print(f"  - {f}")
                if len(files) > 3:
                    print(f"  ... 还有 {len(files)-3} 个文件")
                    
    except Exception as e:
        print(f"❌ 生成过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()